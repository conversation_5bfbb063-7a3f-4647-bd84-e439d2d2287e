#!/usr/bin/env python
"""
Simple test script to verify order creation with product_id functionality
"""
import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/Users/<USER>/apps/outsource/water/water-back-v2')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from apps.products.models.product import Product
from apps.users.models import Vendor, User, UserTypes
from apps.orders.models import Order

def test_product_vendor_relationship():
    """Test that we can get vendor from product"""
    print("Testing product-vendor relationship...")
    
    # Get a sample product
    try:
        product = Product.objects.select_related('vendor').first()
        if product:
            print(f"✓ Found product: {product.title}")
            if product.vendor:
                print(f"✓ Product has vendor: {product.vendor.name}")
                print(f"✓ Vendor ID: {product.vendor.id}")
                return True
            else:
                print("✗ Product has no vendor")
                return False
        else:
            print("✗ No products found in database")
            return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def test_order_creation_logic():
    """Test the order creation logic"""
    print("\nTesting order creation logic...")
    
    # Get a product with vendor
    try:
        product = Product.objects.select_related('vendor').filter(vendor__isnull=False).first()
        if not product:
            print("✗ No products with vendors found")
            return False
            
        print(f"✓ Using product: {product.title}")
        print(f"✓ Product vendor: {product.vendor.name}")
        
        # Simulate the logic from our create method
        product_id = product.id
        order_data = {'product': product_id}
        
        # Get product and vendor (simulating our view logic)
        product_obj = Product.objects.select_related('vendor').get(id=product_id)
        if not product_obj.vendor:
            print("✗ Product has no vendor")
            return False
            
        # Set vendor from product
        order_data['vendor'] = product_obj.vendor.id
        
        print(f"✓ Successfully extracted vendor_id: {order_data['vendor']}")
        print(f"✓ Order data would be: {order_data}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

if __name__ == "__main__":
    print("=== Order Creation Test ===")
    
    test1_passed = test_product_vendor_relationship()
    test2_passed = test_order_creation_logic()
    
    print("\n=== Test Results ===")
    print(f"Product-Vendor Relationship: {'PASS' if test1_passed else 'FAIL'}")
    print(f"Order Creation Logic: {'PASS' if test2_passed else 'FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The order creation modification should work correctly.")
    else:
        print("\n❌ Some tests failed. Please check the database setup.")
